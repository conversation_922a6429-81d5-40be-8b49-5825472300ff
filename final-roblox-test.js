const luamin = require('./luamin.js');

// Comprehensive Roblox test with various Lua constructs
const robloxTestCode = `
-- Advanced Roblox script test
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoid = character:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")

-- Function with multiple parameters and return values
local function calculateDamage(baseDamage, multiplier, critChance)
    local damage = baseDamage * multiplier
    local isCrit = math.random() < critChance
    
    if isCrit then
        damage = damage * 2
        print("Critical hit! Damage: " .. damage)
    else
        print("Normal hit. Damage: " .. damage)
    end
    
    return damage, isCrit
end

-- Table with nested structures
local weaponStats = {
    sword = {
        damage = 25,
        speed = 1.2,
        range = 5
    },
    bow = {
        damage = 15,
        speed = 0.8,
        range = 50
    }
}

-- Loop through weapons
for weaponName, stats in pairs(weaponStats) do
    local damage, crit = calculateDamage(stats.damage, stats.speed, 0.1)
    print(weaponName .. " final damage: " .. damage)
end

-- Event connections
local connection = RunService.Heartbeat:Connect(function(deltaTime)
    if humanoid.Health <= 0 then
        print("Player died!")
        connection:Disconnect()
    end
end)

-- String concatenation and math operations
local message = "Player " .. player.Name .. " has " .. humanoid.Health .. " health"
local healthPercent = math.floor((humanoid.Health / humanoid.MaxHealth) * 100)

print(message)
print("Health: " .. healthPercent .. "%")
`;

console.log('🧪 COMPREHENSIVE ROBLOX LUA 5.1 COMPATIBILITY TEST');
console.log('=' .repeat(60));
console.log('\n📝 Original Roblox code:');
console.log(robloxTestCode);
console.log('\n' + '='.repeat(60) + '\n');

try {
    const obfuscated = luamin.minify(robloxTestCode);
    console.log('🔒 Obfuscated Roblox-compatible code:');
    console.log(obfuscated);
    
    // Comprehensive syntax validation
    const issues = [];
    
    // 1. Check for line breaks in strings
    if (obfuscated.match(/"[^"]*\n[^"]*"/)) {
        issues.push('❌ Line breaks found in string literals');
    } else {
        console.log('✅ No line breaks in string literals');
    }
    
    // 2. Check for balanced parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push(`❌ Unbalanced parentheses: ${openParens} open, ${closeParens} close`);
    } else {
        console.log('✅ Balanced parentheses');
    }
    
    // 3. Check for balanced brackets
    const openBrackets = (obfuscated.match(/\[/g) || []).length;
    const closeBrackets = (obfuscated.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push(`❌ Unbalanced brackets: ${openBrackets} open, ${closeBrackets} close`);
    } else {
        console.log('✅ Balanced brackets');
    }
    
    // 4. Check for balanced braces
    const openBraces = (obfuscated.match(/\{/g) || []).length;
    const closeBraces = (obfuscated.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
        issues.push(`❌ Unbalanced braces: ${openBraces} open, ${closeBraces} close`);
    } else {
        console.log('✅ Balanced braces');
    }
    
    // 5. Check for Lua 5.2+ features that shouldn't be present
    if (obfuscated.match(/goto\s+\w+|::\w+::/)) {
        issues.push('❌ Lua 5.2+ goto statements found');
    } else {
        console.log('✅ No Lua 5.2+ goto statements');
    }
    
    // 6. Check for bitwise operators (should not be present)
    if (obfuscated.match(/[&|~]|<<|>>/)) {
        issues.push('❌ Lua 5.3+ bitwise operators found');
    } else {
        console.log('✅ No Lua 5.3+ bitwise operators');
    }
    
    // 7. Check that math.fmod is used instead of % for modulo in decode functions
    if (obfuscated.includes('math.fmod')) {
        console.log('✅ Using Lua 5.1 compatible math.fmod');
    }
    
    // 8. Check for proper function syntax
    if (obfuscated.match(/function\s*\(/)) {
        console.log('✅ Proper function syntax');
    }
    
    // 9. Check code size and compression
    const originalLength = robloxTestCode.length;
    const obfuscatedLength = obfuscated.length;
    const sizeChange = ((obfuscatedLength - originalLength) / originalLength * 100).toFixed(1);
    
    console.log('\n📊 OBFUSCATION STATISTICS:');
    console.log('=' .repeat(40));
    console.log(`📏 Original size: ${originalLength} characters`);
    console.log(`📏 Obfuscated size: ${obfuscatedLength} characters`);
    console.log(`📈 Size change: +${sizeChange}%`);
    
    // Count obfuscated elements
    const stringMatches = obfuscated.match(/local \w+={[^}]+}/);
    const stringCount = stringMatches ? (stringMatches[0].match(/"/g) || []).length / 2 : 0;
    console.log(`🔤 Strings obfuscated: ${stringCount}`);
    
    const functionMatches = obfuscated.match(/local function \w+/g) || [];
    console.log(`🔧 Functions obfuscated: ${functionMatches.length}`);
    
    if (issues.length === 0) {
        console.log('\n🎉 ALL SYNTAX CHECKS PASSED!');
        console.log('✅ Code is ready for Roblox Studio');
        console.log('✅ Lua 5.1 compatible');
        console.log('✅ No syntax errors detected');
        console.log('✅ Proper obfuscation applied');
    } else {
        console.log('\n⚠️  ISSUES FOUND:');
        issues.forEach(issue => console.log(issue));
    }
    
} catch (error) {
    console.error('❌ CRITICAL ERROR during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
