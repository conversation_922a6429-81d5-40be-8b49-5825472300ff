const luamin = require('./luamin.js');

// Test the exact case that was failing - string concatenation with function call
const testCode = `
local x = 5
print("Result: " .. x)
`;

console.log('Testing string concatenation...\n');

try {
    const obfuscated = luamin.minify(testCode);
    console.log('Obfuscated code:');
    console.log(obfuscated);
    
    // Count parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    
    console.log(`\nOpen parentheses: ${openParens}`);
    console.log(`Close parentheses: ${closeParens}`);
    
    if (openParens !== closeParens) {
        console.log('❌ String concatenation case is unbalanced!');
        
        // Let's find the exact issue
        let balance = 0;
        let issues = [];
        for (let i = 0; i < obfuscated.length; i++) {
            const char = obfuscated[i];
            if (char === '(') {
                balance++;
            } else if (char === ')') {
                balance--;
                if (balance < 0) {
                    issues.push({
                        position: i,
                        context: obfuscated.substring(Math.max(0, i-20), i+20),
                        balance: balance
                    });
                }
            }
        }
        
        console.log(`Final balance: ${balance}`);
        if (issues.length > 0) {
            console.log('Issues found:');
            issues.forEach((issue, idx) => {
                console.log(`${idx + 1}. Position ${issue.position}, balance ${issue.balance}: "${issue.context}"`);
            });
        }
    } else {
        console.log('✅ String concatenation case is balanced');
    }
    
} catch (error) {
    console.error('Error:', error.message);
}
