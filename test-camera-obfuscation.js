const fs = require('fs');
const luamin = require('./luamin.js');

// Read the camera test script
const cameraScript = fs.readFileSync('camera-test-script.lua', 'utf8');

console.log('🎥 TESTING CAMERA-SAFE SCRIPT OBFUSCATION');
console.log('=' .repeat(50));
console.log('\n📝 Original camera script:');
console.log(cameraScript);
console.log('\n' + '='.repeat(50) + '\n');

try {
    const obfuscated = luamin.minify(cameraScript);
    console.log('🔒 Obfuscated camera-safe script:');
    console.log(obfuscated);
    
    // Save the obfuscated version
    fs.writeFileSync('camera-test-obfuscated.lua', obfuscated);
    
    // Syntax validation
    const issues = [];
    
    // Check for balanced parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push(`❌ Unbalanced parentheses: ${openParens} open, ${closeParens} close`);
    } else {
        console.log('✅ Balanced parentheses');
    }
    
    // Check for balanced brackets
    const openBrackets = (obfuscated.match(/\[/g) || []).length;
    const closeBrackets = (obfuscated.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push(`❌ Unbalanced brackets: ${openBrackets} open, ${closeBrackets} close`);
    } else {
        console.log('✅ Balanced brackets');
    }
    
    // Check for balanced braces
    const openBraces = (obfuscated.match(/\{/g) || []).length;
    const closeBraces = (obfuscated.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
        issues.push(`❌ Unbalanced braces: ${openBraces} open, ${closeBraces} close`);
    } else {
        console.log('✅ Balanced braces');
    }
    
    // Check for camera-related safety
    if (obfuscated.includes('CurrentCamera')) {
        console.log('✅ CurrentCamera references preserved');
    }
    
    if (obfuscated.includes('wait')) {
        console.log('✅ Wait function calls preserved');
    }
    
    console.log('\n📊 OBFUSCATION RESULTS:');
    console.log('=' .repeat(30));
    console.log(`📏 Original size: ${cameraScript.length} characters`);
    console.log(`📏 Obfuscated size: ${obfuscated.length} characters`);
    console.log(`📈 Size change: +${((obfuscated.length - cameraScript.length) / cameraScript.length * 100).toFixed(1)}%`);
    
    if (issues.length === 0) {
        console.log('\n🎉 CAMERA SCRIPT OBFUSCATION SUCCESSFUL!');
        console.log('✅ No syntax errors detected');
        console.log('✅ Camera safety measures preserved');
        console.log('✅ Ready for Roblox deployment');
        console.log('\n💡 The obfuscated script includes safe camera access patterns');
        console.log('💡 This should prevent "attempt to index nil with CurrentCamera" errors');
    } else {
        console.log('\n⚠️  ISSUES FOUND:');
        issues.forEach(issue => console.log(issue));
    }
    
} catch (error) {
    console.error('❌ ERROR during obfuscation:', error.message);
    console.error('Stack trace:', error.stack);
}
