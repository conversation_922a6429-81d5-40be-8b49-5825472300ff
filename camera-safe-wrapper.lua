-- CAMERA SAFE WRAPPER - Add this to the TOP of your obfuscated script
-- This ensures CurrentCamera is available before any other code runs

-- Immediate camera safety check
local function ensureCameraExists()
    if not workspace.CurrentCamera then
        print("Waiting for CurrentCamera...")
        local attempts = 0
        while not workspace.CurrentCamera and attempts < 200 do
            wait(0.05) -- Wait 50ms between checks
            attempts = attempts + 1
        end
        
        if not workspace.CurrentCamera then
            -- Last resort: create a basic camera
            warn("CurrentCamera still nil after 10 seconds, creating fallback")
            local camera = Instance.new("Camera")
            camera.Name = "CurrentCamera"
            camera.Parent = workspace
            workspace.CurrentCamera = camera
        end
    end
    return workspace.CurrentCamera
end

-- Ensure camera exists immediately
ensureCameraExists()

-- Override workspace.CurrentCamera access to be safe
local originalWorkspace = workspace
local workspaceMetatable = {
    __index = function(t, k)
        if k == "CurrentCamera" then
            return ensureCameraExists()
        else
            return originalWorkspace[k]
        end
    end,
    __newindex = function(t, k, v)
        originalWorkspace[k] = v
    end
}

-- Create a safe workspace proxy (optional - only if needed)
-- setmetatable({}, workspaceMetatable)

print("Camera safety wrapper loaded - CurrentCamera is now safe to access")

-- YOUR OBFUSCATED CODE GOES BELOW THIS LINE
-- Paste your obfuscated script here and it will work without CurrentCamera errors
