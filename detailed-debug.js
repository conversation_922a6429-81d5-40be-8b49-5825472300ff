const luamin = require('./luamin.js');

// Test with a simple print statement that should work
const testCode = `
print("Hello")
`;

console.log('Testing simple print statement...\n');

try {
    const obfuscated = luamin.minify(testCode);
    console.log('Obfuscated code:');
    console.log(obfuscated);
    
    // Count parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    
    console.log(`\nOpen parentheses: ${openParens}`);
    console.log(`Close parentheses: ${closeParens}`);
    
    if (openParens !== closeParens) {
        console.log('❌ Even simple case is unbalanced!');
        
        // Let's examine character by character
        let balance = 0;
        let maxBalance = 0;
        for (let i = 0; i < obfuscated.length; i++) {
            const char = obfuscated[i];
            if (char === '(') {
                balance++;
                maxBalance = Math.max(maxBalance, balance);
            } else if (char === ')') {
                balance--;
                if (balance < 0) {
                    console.log(`❌ Extra closing parenthesis at position ${i}: "${obfuscated.substring(Math.max(0, i-10), i+10)}"`);
                    break;
                }
            }
        }
        console.log(`Final balance: ${balance}, Max depth: ${maxBalance}`);
    } else {
        console.log('✅ Simple case is balanced');
    }
    
} catch (error) {
    console.error('Error:', error.message);
}
