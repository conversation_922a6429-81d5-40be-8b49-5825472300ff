# 🎥 วิธีแก้ปัญหา CurrentCamera สำหรับโค้ดที่เรียกจากที่อื่น

## 🚨 ปัญหาที่เจอ:
```
attempt to index nil with 'CurrentCamera'
```

เกิดขึ้นเมื่อโค้ดมีการเรียกใช้จากที่อื่น เช่น:
- `require(script.Module)`
- `loadstring(code)()`
- การเรียกฟังก์ชันจากสคริปต์อื่น

## ⚡ วิธีแก้แบบง่าย (Quick Fix):

### 1. ใส่ที่ต้นสคริปต์หลัก:
```lua
repeat wait() until workspace.CurrentCamera
_G.safeCamera = function()
    repeat wait() until workspace.CurrentCamera
    return workspace.CurrentCamera
end
```

### 2. ในโค้ดที่เรียกจากที่อื่น ให้เปลี่ยนจาก:
```lua
-- เดิม (จะ Error)
local camera = workspace.CurrentCamera
```

### เป็น:
```lua
-- ใหม่ (ปลอดภัย)
local camera = _G.safeCamera()
```

## 🛡️ วิธีแก้แบบสมบูรณ์:

### ใช้ไฟล์ `complete-solution-thai.lua`:

1. **วางไฟล์นี้ในสคริปต์หลัก** (ที่รันก่อน)
2. **ใช้ฟังก์ชันเหล่านี้ในโค้ดอื่นๆ:**

```lua
-- แทน workspace.CurrentCamera
local camera = _G.getCamera()

-- แทน loadstring
local func = _G.safeLoadstring("print(workspace.CurrentCamera)")

-- แทน require
local module = _G.safeRequire(script.MyModule)
```

## 📋 ขั้นตอนการแก้ไข:

### ขั้นตอนที่ 1: เพิ่ม Safety Code
```lua
-- ใส่ที่ต้นสคริปต์หลัก
repeat wait() until workspace.CurrentCamera
print("Camera พร้อมแล้ว!")

-- สร้างฟังก์ชันสำหรับโค้ดอื่น
_G.getCamera = function()
    if not workspace.CurrentCamera then
        repeat wait(0.1) until workspace.CurrentCamera
    end
    return workspace.CurrentCamera
end
```

### ขั้นตอนที่ 2: แก้ไขโค้ดที่เรียกจากที่อื่น
```lua
-- ใน Module หรือ loadstring
-- เปลี่ยนจาก:
local camera = workspace.CurrentCamera

-- เป็น:
local camera = _G.getCamera()
```

### ขั้นตอนที่ 3: สำหรับ loadstring
```lua
-- แทนที่จะใช้:
loadstring(code)()

-- ใช้:
_G.safeLoadstring(code)()
```

## 🎯 ตัวอย่างการใช้งานจริง:

### สคริปต์หลัก:
```lua
-- Main Script
repeat wait() until workspace.CurrentCamera
_G.getCamera = function()
    repeat wait() until workspace.CurrentCamera
    return workspace.CurrentCamera
end

-- เรียกใช้โค้ดอื่น
require(script.CameraModule)
```

### Module Script:
```lua
-- CameraModule
local camera = _G.getCamera() -- ปลอดภัย!
camera.CameraType = Enum.CameraType.Custom
print("Camera setup เสร็จแล้ว!")
```

## 🔧 สำหรับโค้ด Obfuscated:

### วิธีที่ 1: เพิ่มที่ต้นโค้ด
```lua
repeat wait() until workspace.CurrentCamera
_G.cam = workspace.CurrentCamera

-- โค้ด obfuscated ของคุณ...
```

### วิธีที่ 2: ใช้ Complete Solution
1. รันไฟล์ `complete-solution-thai.lua` ก่อน
2. รันโค้ด obfuscated ของคุณ
3. ไม่ต้องแก้ไขอะไรเพิ่ม!

## ✅ เช็คลิสต์:

- [ ] ใส่ `repeat wait() until workspace.CurrentCamera` ที่ต้นสคริปต์หลัก
- [ ] สร้าง `_G.getCamera()` function
- [ ] เปลี่ยน `workspace.CurrentCamera` เป็น `_G.getCamera()` ในโค้ดอื่น
- [ ] ทดสอบในทั้ง Studio และ Game จริง
- [ ] ตรวจสอบว่าไม่มี Error แล้ว

## 🚀 วิธีใช้ไฟล์ที่ฉันสร้างให้:

1. **`simple-camera-fix-thai.lua`** - วิธีแก้แบบง่าย
2. **`complete-solution-thai.lua`** - วิธีแก้แบบสมบูรณ์
3. **`universal-camera-fix.lua`** - วิธีแก้แบบครอบคลุมทุกกรณี

เลือกใช้ตามความต้องการ!

## 💡 เคล็ดลับ:

- ใช้ `_G` เพื่อแชร์ฟังก์ชันระหว่างสคริปต์
- ใส่ camera safety ในสคริปต์หลักที่รันก่อน
- ทดสอบในสภาพแวดล้อมจริงเสมอ
- ใช้ `wait()` เพื่อป้องกัน infinite loop
