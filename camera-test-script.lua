-- CAMERA TEST SCRIPT - This will be obfuscated
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer

-- Safe camera access function
local function getCameraSafely()
    local camera = workspace.CurrentCamera
    local attempts = 0
    
    while not camera and attempts < 50 do
        wait(0.1)
        camera = workspace.CurrentCamera
        attempts = attempts + 1
    end
    
    if not camera then
        error("Could not access CurrentCamera")
    end
    
    return camera
end

-- Wait for character
local character = player.Character or player.CharacterAdded:Wait()
local humanoid = character:WaitFor<PERSON>hild("Humanoid")

-- Get camera safely
local camera = getCameraSafely()

print("Camera accessed successfully!")
print("Camera Type: " .. tostring(camera.CameraType))
print("Camera Subject: " .. tostring(camera.CameraSubject))

-- Set camera properties
camera.CameraSubject = humanoid
camera.CameraType = Enum.CameraType.Custom

-- Camera update loop
local connection = RunService.Heartbeat:Connect(function()
    if camera and camera.Parent then
        -- Camera is valid, do camera operations here
        local cf = camera.CFrame
        -- Example: Log camera position every 60 frames
        if tick() % 1 < 0.016 then
            print("Camera Position: " .. tostring(cf.Position))
        end
    else
        -- Camera became invalid, get it again
        camera = getCameraSafely()
        if camera then
            camera.CameraSubject = humanoid
        end
    end
end)

-- Handle respawning
player.CharacterAdded:Connect(function(newCharacter)
    character = newCharacter
    humanoid = character:WaitForChild("Humanoid")
    camera = getCameraSafely()
    camera.CameraSubject = humanoid
    print("Player respawned - camera updated")
end)
