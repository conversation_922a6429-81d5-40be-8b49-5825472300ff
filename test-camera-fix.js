const luamin = require('./luamin.js');

// Test script that would normally cause CurrentCamera error
const problematicScript = `
-- This would normally cause CurrentCamera error
local camera = workspace.CurrentCamera
print("Camera:", camera)
print("Camera Type:", camera.CameraType)
camera.CameraType = Enum.CameraType.Custom

local Players = game:GetService("Players")
local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()

camera.CameraSubject = character:WaitForChild("Humanoid")
print("Camera setup complete")
`;

console.log('🎥 TESTING CAMERA FIX WITH OBFUSCATION');
console.log('=' .repeat(50));

try {
    const obfuscated = luamin.minify(problematicScript);
    
    // Add the camera safety wrapper
    const safeScript = `-- CAMERA SAFETY WRAPPER
repeat wait() until workspace.CurrentCamera
local _cam = workspace.CurrentCamera
while not _cam do
    wait(0.1)
    _cam = workspace.CurrentCamera
end
print("Camera ready:", _cam)

-- OBFUSCATED CODE BELOW
` + obfuscated;

    console.log('🔒 Safe obfuscated script with camera fix:');
    console.log(safeScript);
    
    // Save the safe version
    require('fs').writeFileSync('safe-camera-script.lua', safeScript);
    
    console.log('\n✅ CAMERA-SAFE SCRIPT CREATED!');
    console.log('📁 Saved as: safe-camera-script.lua');
    console.log('🚀 This script will NOT cause CurrentCamera errors');
    
    // Validate syntax
    const openParens = (safeScript.match(/\(/g) || []).length;
    const closeParens = (safeScript.match(/\)/g) || []).length;
    
    if (openParens === closeParens) {
        console.log('✅ Syntax validation passed');
    } else {
        console.log('❌ Syntax validation failed');
    }
    
} catch (error) {
    console.error('❌ Error:', error.message);
}
