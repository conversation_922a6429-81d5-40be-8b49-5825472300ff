const luamin = require('./luamin.js');

// Simple test to check parentheses balance
const simpleCode = `
local function test(a, b)
    return a + b
end

local result = test(5, 10)
print(result)
`;

console.log('Testing parentheses balance...\n');

try {
    const obfuscated = luamin.minify(simpleCode);
    console.log('Obfuscated code:');
    console.log(obfuscated);
    
    // Count parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    
    console.log(`\nOpen parentheses: ${openParens}`);
    console.log(`Close parentheses: ${closeParens}`);
    console.log(`Difference: ${openParens - closeParens}`);
    
    if (openParens === closeParens) {
        console.log('✅ Parentheses are balanced');
    } else {
        console.log('❌ Parentheses are NOT balanced');
        
        // Let's find where the issue might be
        console.log('\nAnalyzing code structure...');
        
        // Split by common delimiters and check each part
        const parts = obfuscated.split(/[;]/);
        for (let i = 0; i < parts.length; i++) {
            const part = parts[i].trim();
            if (part) {
                const partOpenParens = (part.match(/\(/g) || []).length;
                const partCloseParens = (part.match(/\)/g) || []).length;
                if (partOpenParens !== partCloseParens) {
                    console.log(`Part ${i}: "${part.substring(0, 50)}..." - Open: ${partOpenParens}, Close: ${partCloseParens}`);
                }
            }
        }
    }
    
} catch (error) {
    console.error('Error:', error.message);
}
