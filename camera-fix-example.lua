-- ROBLOX CAMERA FIX EXAMPLES
-- These examples show how to safely access CurrentCamera

-- Method 1: Wait for CurrentCamera to exist
local function waitForCamera()
    local camera = workspace.CurrentCamera
    while not camera do
        wait(0.1)
        camera = workspace.CurrentCamera
    end
    return camera
end

-- Method 2: Use RunService to wait for camera
local RunService = game:GetService("RunService")
local function getCameraSafely()
    local camera = workspace.CurrentCamera
    if not camera then
        -- Wait for the camera to be available
        local connection
        connection = RunService.Heartbeat:Connect(function()
            camera = workspace.CurrentCamera
            if camera then
                connection:Disconnect()
            end
        end)
        -- Wait until camera is available
        while not camera do
            wait()
        end
    end
    return camera
end

-- Method 3: Simple nil check with retry
local function getCameraWithRetry()
    local camera = workspace.CurrentCamera
    local attempts = 0
    while not camera and attempts < 50 do
        wait(0.1)
        camera = workspace.CurrentCamera
        attempts = attempts + 1
    end
    
    if not camera then
        warn("Could not get CurrentCamera after 5 seconds")
        return nil
    end
    
    return camera
end

-- Method 4: Event-based approach
local function onCameraReady(callback)
    local camera = workspace.CurrentCamera
    if camera then
        callback(camera)
    else
        local connection
        connection = workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function()
            camera = workspace.CurrentCamera
            if camera then
                connection:Disconnect()
                callback(camera)
            end
        end)
    end
end

-- Example usage:
print("Testing camera access methods...")

-- Safe camera access
local camera = getCameraWithRetry()
if camera then
    print("Camera found:", camera)
    print("Camera CFrame:", camera.CFrame)
    print("Camera Type:", camera.CameraType)
else
    print("Failed to get camera")
end

-- Event-based usage
onCameraReady(function(cam)
    print("Camera is ready:", cam)
    -- Your camera-related code here
end)
