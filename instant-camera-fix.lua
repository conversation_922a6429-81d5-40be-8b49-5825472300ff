-- INSTANT CAMERA FIX - Copy and paste this at the TOP of your script

-- Wait for CurrentCamera to exist
repeat wait() until workspace.CurrentCamera

-- Alternative method - force wait for camera
local camera = workspace.CurrentCamera
while not camera do
    wait(0.1)
    camera = workspace.CurrentCamera
end

print("CurrentCamera is now available:", camera)

-- NOW YOUR OBFUSCATED CODE WILL WORK
-- Paste your obfuscated script below this line
