const luamin = require('./luamin.js');

// Simple test code to verify syntax
const testCode = `
local function test()
    print("Hello World")
    local x = 10
    local y = x * 2
    return y
end

local result = test()
print("Result: " .. result)
`;

console.log('Testing syntax correctness...\n');
console.log('Original code:');
console.log(testCode);
console.log('\n' + '='.repeat(50) + '\n');

try {
    const obfuscated = luamin.minify(testCode);
    console.log('Obfuscated code:');
    console.log(obfuscated);
    
    // Check for common syntax issues
    const issues = [];
    
    // Check for line breaks in strings
    if (obfuscated.match(/"[^"]*\n[^"]*"/)) {
        issues.push('❌ Line breaks found in string literals');
    } else {
        console.log('✅ No line breaks in string literals');
    }
    
    // Check for proper spacing around keywords
    if (obfuscated.match(/\w(if|then|end|local|function)\w/)) {
        issues.push('❌ Missing spaces around keywords');
    } else {
        console.log('✅ Proper spacing around keywords');
    }
    
    // Check for balanced parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push(`❌ Unbalanced parentheses: ${openParens} open, ${closeParens} close`);
    } else {
        console.log('✅ Balanced parentheses');
    }
    
    // Check for balanced brackets
    const openBrackets = (obfuscated.match(/\[/g) || []).length;
    const closeBrackets = (obfuscated.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push(`❌ Unbalanced brackets: ${openBrackets} open, ${closeBrackets} close`);
    } else {
        console.log('✅ Balanced brackets');
    }
    
    // Check for balanced braces
    const openBraces = (obfuscated.match(/\{/g) || []).length;
    const closeBraces = (obfuscated.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
        issues.push(`❌ Unbalanced braces: ${openBraces} open, ${closeBraces} close`);
    } else {
        console.log('✅ Balanced braces');
    }
    
    if (issues.length === 0) {
        console.log('\n🎉 All syntax checks passed! Code should work in Roblox.');
    } else {
        console.log('\n⚠️  Issues found:');
        issues.forEach(issue => console.log(issue));
    }
    
} catch (error) {
    console.error('❌ ERROR during obfuscation:', error.message);
}
