-- โซลูชันสมบูรณ์แก้ปัญหา CurrentCamera ทุกกรณี
-- รวมถึงการเรียกโค้ดจาก require, loadstring, หรือสคริปต์อื่นๆ

print("🎥 กำลังโหลด Complete Camera Solution...")

-- ฟังก์ชันหลักรอ Camera
local function ensureCameraExists()
    local camera = workspace.CurrentCamera
    local attempts = 0
    
    -- รอสูงสุด 10 วินาที
    while not camera and attempts < 100 do
        wait(0.1)
        camera = workspace.CurrentCamera
        attempts = attempts + 1
        
        if attempts % 10 == 0 then
            print("รอ CurrentCamera... (" .. attempts .. "/100)")
        end
    end
    
    -- ถ้ายังไม่มี Camera ให้สร้างใหม่
    if not camera then
        warn("สร้าง CurrentCamera ใหม่")
        camera = Instance.new("Camera")
        camera.Name = "CurrentCamera"
        camera.Parent = workspace
        workspace.CurrentCamera = camera
        
        -- ตั้งค่าพื้นฐาน
        camera.CameraType = Enum.CameraType.Custom
        camera.FieldOfView = 70
    end
    
    return camera
end

-- รอให้ Camera พร้อมทันที
local mainCamera = ensureCameraExists()
print("✅ CurrentCamera พร้อมใช้งาน:", mainCamera)

-- สร้าง Global Functions สำหรับใช้ในโค้ดอื่น
_G.getCamera = ensureCameraExists
_G.waitForCamera = ensureCameraExists
_G.safeCamera = ensureCameraExists

-- ฟังก์ชันสำหรับ loadstring ที่ปลอดภัย
_G.safeLoadstring = function(code, chunkname)
    if not code then return nil end
    
    -- เพิ่มการตรวจสอบ Camera ลงในโค้ด
    local safeCode = [[
-- Auto Camera Safety
if not workspace.CurrentCamera then
    repeat wait(0.05) until workspace.CurrentCamera
end
]] .. code
    
    local success, result = pcall(loadstring, safeCode, chunkname)
    if success then
        return result
    else
        -- ถ้าเพิ่ม safety code แล้วเกิดข้อผิดพลาด ให้ใช้โค้ดเดิม
        return loadstring(code, chunkname)
    end
end

-- ฟังก์ชันสำหรับ require ที่ปลอดภัย
_G.safeRequire = function(module)
    -- ตรวจสอบ Camera ก่อน require
    ensureCameraExists()
    return require(module)
end

-- Override workspace.CurrentCamera ให้ปลอดภัยเสมอ
local workspaceMetatable = getmetatable(workspace) or {}
local originalIndex = workspaceMetatable.__index

workspaceMetatable.__index = function(t, k)
    if k == "CurrentCamera" then
        return ensureCameraExists()
    elseif originalIndex then
        return originalIndex(t, k)
    else
        return rawget(t, k)
    end
end

setmetatable(workspace, workspaceMetatable)

-- สร้าง Event Handler สำหรับกรณีที่ Camera หาย
workspace:GetPropertyChangedSignal("CurrentCamera"):Connect(function()
    if not workspace.CurrentCamera then
        warn("CurrentCamera หายไป! กำลังสร้างใหม่...")
        ensureCameraExists()
    end
end)

-- ฟังก์ชันตรวจสอบและแก้ไข Camera อัตโนมัติ
local function monitorCamera()
    spawn(function()
        while true do
            wait(1) -- ตรวจสอบทุกวินาที
            if not workspace.CurrentCamera then
                warn("ตรวจพบ CurrentCamera = nil, กำลังแก้ไข...")
                ensureCameraExists()
            end
        end
    end)
end

-- เริ่มการตรวจสอบ
monitorCamera()

print("🎯 Complete Camera Solution โหลดเสร็จแล้ว!")
print("📋 ฟังก์ชันที่ใช้ได้:")
print("   • _G.getCamera() - รับ Camera ที่ปลอดภัย")
print("   • _G.safeLoadstring(code) - loadstring ปลอดภัย")
print("   • _G.safeRequire(module) - require ปลอดภัย")
print("   • workspace.CurrentCamera - ปลอดภัยอัตโนมัติ")
print("🛡️  Camera จะถูกตรวจสอบและแก้ไขอัตโนมัติ")

-- ตัวอย่างการใช้งาน:
--[[
-- วิธีใช้ในโค้ดอื่น:

-- แทนที่:
local camera = workspace.CurrentCamera

-- ใช้:
local camera = _G.getCamera()

-- สำหรับ loadstring:
local func = _G.safeLoadstring("print('Camera:', workspace.CurrentCamera)")
func()

-- สำหรับ require:
local myModule = _G.safeRequire(script.MyModule)
--]]
