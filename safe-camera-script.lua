-- CAMERA SAFETY WRAPPER
repeat wait() until workspace.CurrentCamera
local _cam = workspace.CurrentCamera
while not _cam do
    wait(0.1)
    _cam = workspace.CurrentCamera
end
print("Camera ready:", _cam)

-- OBFUSCATED CODE BELOW
local gRTZKJqCZ={"Q2FtZXJhOgAA!","Q2FtZ;XJhIFR5cGU6>","UG`xheWVycwAA.","SHVtYW5vaWQA?","Q2FtZXJhIHNldHVwIGNvbXBsZXRl<"};local function VyQQLILE(ZrWXwLZGTx) local YqWunTwgjE="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=" local sXTpypzOjiIc="" for kcNTOVkKjvRlU=1,#ZrWXwLZGTx do local tutRgYPfvXoJ=ZrWXwLZGTx:sub(kcNTOVkKjvRlU,kcNTOVkKjvRlU) if YqWunTwgjE:find(tutRgYPfvXoJ,1,true) then sXTpypzOjiIc=sXTpypzOjiIc..tutRgYPfvXoJ end end return sXTpypzOjiIc end;local function MdBfdEvKcI(hKaOoUBkVfn) local gReejlGUBql="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/" local wcupuAGG={} for fNoAuYzreFUmr=1,#gReejlGUBql do wcupuAGG[gReejlGUBql:sub(fNoAuYzreFUmr,fNoAuYzreFUmr)]=fNoAuYzreFUmr-1 end local EEfRuxye="" for xHWWhEEC=1,#hKaOoUBkVfn,4 do local GJiLeTcvn,LIoJGdXVApxzK,eWAkJeySayTl,PUXlmBFcnEZez=wcupuAGG[hKaOoUBkVfn:sub(xHWWhEEC,xHWWhEEC)],wcupuAGG[hKaOoUBkVfn:sub(xHWWhEEC+1,xHWWhEEC+1)],wcupuAGG[hKaOoUBkVfn:sub(xHWWhEEC+2,xHWWhEEC+2)],wcupuAGG[hKaOoUBkVfn:sub(xHWWhEEC+3,xHWWhEEC+3)] if GJiLeTcvn and LIoJGdXVApxzK then local NxHqEGTfGRTmQ=GJiLeTcvn*4+math.floor(LIoJGdXVApxzK/16) EEfRuxye=EEfRuxye..string.char(NxHqEGTfGRTmQ) if eWAkJeySayTl then NxHqEGTfGRTmQ=math.fmod(LIoJGdXVApxzK,16)*16+math.floor(eWAkJeySayTl/4) EEfRuxye=EEfRuxye..string.char(NxHqEGTfGRTmQ) end if PUXlmBFcnEZez then NxHqEGTfGRTmQ=math.fmod(eWAkJeySayTl,4)*64+PUXlmBFcnEZez EEfRuxye=EEfRuxye..string.char(NxHqEGTfGRTmQ) end end end return EEfRuxye end;local function sBxpIUsitN(s) return MdBfdEvKcI(VyQQLILE(s)) end;local function cOTNUJFNEl(id,...) if id==1 then return print(...) end end;local function HMOEQTAK() local cam = workspace.CurrentCamera local attempts = 0 while not cam and attempts < 100 do wait(0.03) cam = workspace.CurrentCamera attempts = attempts + 1 end if not cam then error("CurrentCamera not available") end return cam end;workspace.CurrentCamera = workspace.CurrentCamera or HMOEQTAK();local QqEciuQXYGCD=workspace.CurrentCamera;cOTNUJFNEl(1,sBxpIUsitN(gRTZKJqCZ[1]),QqEciuQXYGCD)cOTNUJFNEl(1,sBxpIUsitN(gRTZKJqCZ[2]),QqEciuQXYGCD.CameraType)QqEciuQXYGCD.CameraType=Enum.CameraType.Custom;local lkYiHmRIde=game:GetService(sBxpIUsitN(gRTZKJqCZ[3]))local cZKaeCaugo=lkYiHmRIde.LocalPlayer;local QCDimHeqqt=cZKaeCaugo.Character or cZKaeCaugo.CharacterAdded:Wait()QqEciuQXYGCD.CameraSubject=QCDimHeqqt:WaitForChild(sBxpIUsitN(gRTZKJqCZ[4]))cOTNUJFNEl(1,sBxpIUsitN(gRTZKJqCZ[5]))