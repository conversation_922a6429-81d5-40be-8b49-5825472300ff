const luamin = require('./luamin.js');

// Test with math.random() which might be causing the issue
const testCode = `
local x = math.random()
local y = math.random(1, 10)
print("Random: " .. x)
print("Random range: " .. y)
`;

console.log('Testing math.random() parentheses...\n');

try {
    const obfuscated = luamin.minify(testCode);
    console.log('Obfuscated code:');
    console.log(obfuscated);
    
    // Count parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    
    console.log(`\nOpen parentheses: ${openParens}`);
    console.log(`Close parentheses: ${closeParens}`);
    console.log(`Difference: ${openParens - closeParens}`);
    
    if (openParens !== closeParens) {
        console.log('❌ Parentheses are NOT balanced');
        
        // Let's examine the dispatcher calls
        const dispatcherCalls = obfuscated.match(/\w+\(\d+[^)]*\)/g) || [];
        console.log('\nDispatcher calls found:');
        dispatcherCalls.forEach((call, i) => {
            console.log(`${i + 1}: ${call}`);
            const callOpenParens = (call.match(/\(/g) || []).length;
            const callCloseParens = (call.match(/\)/g) || []).length;
            if (callOpenParens !== callCloseParens) {
                console.log(`   ❌ Unbalanced: ${callOpenParens} open, ${callCloseParens} close`);
            }
        });
    } else {
        console.log('✅ Parentheses are balanced');
    }
    
} catch (error) {
    console.error('Error:', error.message);
}
