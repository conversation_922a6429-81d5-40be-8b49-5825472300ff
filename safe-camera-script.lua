-- SAFE CAMERA ACCESS SCRIPT FOR ROBLOX
-- This script demonstrates how to safely access CurrentCamera

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Safe function to get the camera
local function getCurrentCamera()
    local camera = workspace.CurrentCamera
    if not camera then
        -- Wait for camera to become available
        print("Waiting for CurrentCamera...")
        local attempts = 0
        while not camera and attempts < 100 do
            wait(0.1)
            camera = workspace.CurrentCamera
            attempts = attempts + 1
        end
        
        if not camera then
            error("CurrentCamera is not available after 10 seconds")
        end
    end
    return camera
end

-- Wait for character and camera to be ready
local function waitForPlayerReady()
    -- Wait for character
    local character = player.Character or player.CharacterAdded:Wait()
    local humanoid = character:WaitFor<PERSON>hild("Humanoid")
    
    -- Wait for camera
    local camera = getCurrentCamera()
    
    return character, humanoid, camera
end

-- Main script execution
local function main()
    print("Starting safe camera script...")
    
    -- Safely get player components
    local character, humanoid, camera = waitForPlayerReady()
    
    print("Player ready!")
    print("Character:", character.Name)
    print("Humanoid:", humanoid)
    print("Camera:", camera)
    print("Camera CFrame:", camera.CFrame)
    print("Camera Type:", camera.CameraType)
    
    -- Example: Set camera to follow the player
    camera.CameraSubject = humanoid
    camera.CameraType = Enum.CameraType.Custom
    
    -- Example: Camera manipulation
    local originalCFrame = camera.CFrame
    
    -- Smooth camera movement example
    local connection = RunService.Heartbeat:Connect(function()
        if camera and camera.Parent then
            -- Your camera code here
            -- Example: Slight camera shake
            local shake = Vector3.new(
                math.random(-1, 1) * 0.1,
                math.random(-1, 1) * 0.1,
                math.random(-1, 1) * 0.1
            )
            -- Uncomment the line below for camera shake effect
            -- camera.CFrame = camera.CFrame + shake
        else
            -- Camera was destroyed, get it again
            camera = getCurrentCamera()
        end
    end)
    
    -- Handle player respawning
    player.CharacterAdded:Connect(function(newCharacter)
        print("Player respawned, updating camera...")
        character = newCharacter
        humanoid = character:WaitForChild("Humanoid")
        camera = getCurrentCamera()
        camera.CameraSubject = humanoid
    end)
    
    print("Camera script initialized successfully!")
end

-- Run the main function with error handling
local success, error = pcall(main)
if not success then
    warn("Camera script error:", error)
end
