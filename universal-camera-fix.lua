-- UNIVERSAL CAMERA FIX - แก้ปัญหา CurrentCamera สำหรับทุกกรณี
-- รวมถึงการเรียกโค้ดจากที่อื่น (require, loadstring, etc.)

-- 1. สร้างฟังก์ชันรอ Camera ที่ปลอดภัย
local function waitForCamera()
    local camera = workspace.CurrentCamera
    local attempts = 0
    
    while not camera and attempts < 200 do
        wait(0.05) -- รอ 50ms
        camera = workspace.CurrentCamera
        attempts = attempts + 1
    end
    
    if not camera then
        warn("สร้าง Camera ใหม่เพราะรอนานเกินไป")
        camera = Instance.new("Camera")
        camera.Name = "CurrentCamera"
        camera.Parent = workspace
        workspace.CurrentCamera = camera
    end
    
    return camera
end

-- 2. รอให้ Camera พร้อมใช้งาน
local camera = waitForCamera()
print("Camera พร้อมใช้งาน:", camera)

-- 3. สร้าง Global Function สำหรับโค้ดอื่นๆ ที่จะเรียกใช้
_G.getCameraSafely = waitForCamera
_G.ensureCamera = function()
    if not workspace.CurrentCamera then
        workspace.CurrentCamera = waitForCamera()
    end
    return workspace.CurrentCamera
end

-- 4. Override workspace.CurrentCamera เพื่อความปลอดภัย
local originalWorkspace = workspace
local workspaceProxy = setmetatable({}, {
    __index = function(t, k)
        if k == "CurrentCamera" then
            return _G.ensureCamera()
        else
            return originalWorkspace[k]
        end
    end,
    __newindex = function(t, k, v)
        originalWorkspace[k] = v
    end
})

-- 5. แทนที่ workspace ใน global environment (ถ้าจำเป็น)
-- getfenv().workspace = workspaceProxy

-- 6. สร้างฟังก์ชันสำหรับ loadstring ที่ปลอดภัย
_G.safeLoadstring = function(code)
    -- เพิ่ม camera safety ลงในโค้ดที่จะ load
    local safeCode = [[
        local function ensureCameraSafe()
            if not workspace.CurrentCamera then
                repeat wait(0.05) until workspace.CurrentCamera
            end
            return workspace.CurrentCamera
        end
        workspace.CurrentCamera = workspace.CurrentCamera or ensureCameraSafe()
    ]] .. code
    
    return loadstring(safeCode)
end

-- 7. สร้างฟังก์ชันสำหรับ require ที่ปลอดภัย
local originalRequire = require
_G.safeRequire = function(module)
    -- ตรวจสอบ camera ก่อน require
    _G.ensureCamera()
    return originalRequire(module)
end

-- 8. Hook เข้า loadstring และ require (ถ้าต้องการ)
local originalLoadstring = loadstring
getfenv().loadstring = function(code, chunkname)
    if code and type(code) == "string" and code:find("CurrentCamera") then
        return _G.safeLoadstring(code)
    else
        return originalLoadstring(code, chunkname)
    end
end

print("Universal Camera Fix โหลดเสร็จแล้ว!")
print("ฟังก์ชันที่ใช้ได้:")
print("- _G.getCameraSafely() - รับ camera ที่ปลอดภัย")
print("- _G.ensureCamera() - ตรวจสอบและรับ camera")
print("- _G.safeLoadstring(code) - loadstring ที่ปลอดภัย")
print("- _G.safeRequire(module) - require ที่ปลอดภัย")

-- ตัวอย่างการใช้งาน:
--[[
-- ใช้แทน workspace.CurrentCamera
local cam = _G.getCameraSafely()

-- ใช้แทน loadstring
local func = _G.safeLoadstring("print(workspace.CurrentCamera)")
func()

-- ใช้แทน require
local module = _G.safeRequire(script.SomeModule)
--]]
