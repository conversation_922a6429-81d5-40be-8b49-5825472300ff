-- แก้ปัญหา CurrentCamera แบบง่ายๆ สำหรับโค้ดที่เรียกจากที่อื่น

-- วิธีที่ 1: แก้แบบง่ายที่สุด - ใส่ที่ต้นสคริปต์หลัก
repeat wait() until workspace.CurrentCamera
print("Camera พร้อมแล้ว!")

-- วิธีที่ 2: สร้างฟังก์ชันสำหรับโค้ดอื่นๆ ใช้
_G.waitCamera = function()
    repeat wait() until workspace.CurrentCamera
    return workspace.CurrentCamera
end

-- วิธีที่ 3: แทนที่ workspace.CurrentCamera ให้ปลอดภัย
local originalCurrentCamera = workspace.CurrentCamera
workspace.CurrentCamera = workspace.CurrentCamera or _G.waitCamera()

-- วิธีที่ 4: สำหรับ loadstring หรือ require
_G.safeCamera = function()
    if not workspace.CurrentCamera then
        repeat wait(0.1) until workspace.CurrentCamera
    end
    return workspace.CurrentCamera
end

-- ตัวอย่างการใช้ในโค้ดอื่น:
--[[
-- แทนที่จะเขียน:
local camera = workspace.CurrentCamera

-- ให้เขียน:
local camera = _G.safeCamera()

-- หรือ:
local camera = _G.waitCamera()
--]]

print("Camera Fix พร้อมใช้งาน!")
print("ใช้ _G.safeCamera() แทน workspace.CurrentCamera")
